# AGENTS.md

## Project Overview

Avatar Resizer is a client-side browser-based image resizing tool that transforms a single source image into multiple target sizes simultaneously. All processing happens client-side without requiring server uploads or internet connectivity. This project is built using vanilla JavaScript, HTML, and CSS, with a clean and modern interface design.

## General Instructions

- Don't start a web server to serve files from the workspace.
- Don't add new dependencies, they will be added to the project by a human.
- Don't create or run tests.

## Repository Structure

- `index.html`: Main HTML file for the application.
- `css/styles.css`: Main stylesheet for the application.
- `js/app.js`: Main JavaScript file for the application.
- `js/lib/`: Directory containing third-party libraries (Pica, PhotoSwipe, JSZip)

NEVER EVER READ OR MODIFY ANY FILES INSIDE OF THE `js/lib/` DIRECTORY.

## Libraries

Always use context7 when I need code generation, setup or configuration steps, or library/API documentation. This means you should automatically use the Context7 MCP tools to resolve library id and get library docs without me having to explicitly ask.

Use `get-library-docs` tool to look up documentation for the following libraries:

- `<PERSON><PERSON>`: /nodeca/pica
- `PhotoSwipe`: /dimsemenov/photoswipe
- `JSZip`: /github.com/Stuk/jszip

## Coding Standards

- When creating new functionality, scan the project files and think about all of the existing functionality that could be updated to support the same use case. Avoid creating new functionality unless absolutely necessary.
- If you find yourself writing a lot of code, stop and think if there's a simpler way to achieve the same goal.
- Less code is better than more — aim for the smallest, clearest solution that works.
- Keep the code simple and maintainable.
- Do not add comments unless absolutely necessary, the code should be self-explanatory
- Never use null, always use undefined for optional values

## JavaScript

- Don't use ESM modules at all.
- Use vanilla JavaScript only. No frameworks or libraries except the ones explicitly allowed below.
- Explicitly declare all variables. Use `let` and `const` instead of `var`.
- Use simple, clear and minimal variable and function names. Favour brevity over descriptiveness.
- Prefer single-letter names for short-lived variables in small scopes (e.g. loop indices).
- Create utility functions for repeated code patterns.
- Don't create HTML elements using JavaScript DOM methods. UNLESS absolutely necessary, use HTML template literals for generating HTML content.

## HTML

- Use HTML template literals for generating HTML content.
- Don't create HTML elements using JavaScript DOM methods.
- Create HTML templates in the HTML file and clone them in JavaScript.

### Logical OR vs Nullish Coalescing (|| vs ??)

BEFORE using || or ?? operators, ALWAYS ask these questions:

1. **Is 0 a valid value for this property?** If YES, use ??
2. **Is false a valid value for this property?** If YES, use ??
3. **Is empty string ("") a valid value for this property?** If YES, use ??
4. **Do I want ALL falsy values (0, false, "", null, undefined, NaN) to trigger the default?** If YES, use ||
5. **Do I want ONLY null/undefined to trigger the default?** If YES, use ??

EXAMPLES:
- `width = value || 400` ✓ (0 width is invalid)
- `horizontalOffset = value ?? 50` ✓ (0 offset is valid - left edge)
- `transparentBackground = value ?? false` ✓ (false is valid)
- `name = value || "Default"` ✓ (empty string should default)
- `quality = value ?? 0` ✓ (0 quality is valid)

NEVER assume || is correct. ALWAYS verify the logic first.