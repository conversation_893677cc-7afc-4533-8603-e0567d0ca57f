Read the feature overview document located at `docs/ai/reports/FEATURE_OVERVIEW.md` think about it step by step.

Implement all the features listed in the document using vanilla JavaScript, HTML, and CSS. Do not add any frameworks or libraries. The only libraries used for this project are:

- [Pica](js/lib/pica/pica.min.js) - image resizing
- [PhotoSwipe](js/lib/photoswipe/photoswipe.umd.min.js) - lightbox viewer
- [JSZip](js/lib/jszip/jszip.min.js) - ZIP generation

The existing `index.html` and `css/styles.css` files contains the layout and structure for the application. Build upon this foundation to implement the required features.

Place the core application logic in the `js/app.js` file. This is where you will implement all of application functionality.

Update `index.html` and `css/styles.css` as needed to support the new features.

Follow the agent guidelines in `AGENTS.md`.
